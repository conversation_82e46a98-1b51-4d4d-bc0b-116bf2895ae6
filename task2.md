Task1:

create v2 version of v1 @/v1/DivergenceSignalEA.mq5 expert with following changes:

1- add input to set sl pips with default value 3
2- add option to stop trading after X consecutive losses until manual reset

entry conditions enhancments

1- at 1minute tf, add confirmation that price closes above/below wma50 for 3 candles after cross, and price closes above/below wma200.

2- add optinal condition with input "1h_200_check" (default: true) to check if 1h price closes above/below wma200 in the higher time frame 1h

3- do not open trade if calculated sl is more than 10 pips from current price of entry, make it input with default value 10 [input_var : max_sl_pips=10]

4- do not open trade after winning trade within the same hour, make it input with default value true [input_var : no_trade_after_win=True] and input to set number of candles after win to open new trade [input_var : candles_after_win=10]

Task2:
convert the expert into Tradingview pinescript @version=6 strategy with drawings and alerts
draw divergence lines on RSI, MACD, and Stoch indicators
draw entry signals on the chart
draw stoploss and trailing stoploss levels
draw target levels
draw exit signals on the chart
