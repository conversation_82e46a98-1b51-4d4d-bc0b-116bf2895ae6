Task1: Build MQL5 Indicator
Convert the following trading strategy into indicator and build an MQL5 indicator to print out entry signals on the chart. 

On indicator remove it need to remove all printed drawing
When added to chart it need to analyze previous 2 days and apply the indictor to print any previous entries 

On tick it need to print any new signals on real-time price matching the strategy conditions

Any “input_var” needs to be configured as input with default value

It needs to print a straight line for the divergence on RSI, MAC, and Stoch indicators


Task2: Build MQL5 Exper
After building the indicator, create another version and converted to an Expert that open and close trades based on the strategy condition in addition to applying the Exit, Stoploss, and Trailing stoploss rules in the strategy. It needs to have the same input variables


Strategy Conditions:
This strategy is using high time frame 1hour to define the trading direction buy or sell, and analyze price behaviour on small time frame 1 minute to signal trade entry.

It targets 7-20 pips at least with exit when price cross and close back the wma 50 in the opposite direction of the trade if price moves greater than min_target=(default=7). 
[ input_var : min_target=7] 
[ input_var : max_target=20]

The stoploss is the level of the recent multiple bottoms with divergence in RSI,MACD,Stoch + 3 pips in the opposite direction of the trade 

Trailing stoploss:
1- when price moves 5 pis from entry move sl to breakeven(entry price) [ input_var : TSL1=5]
1- when price moves 10 pips move sl to 3pips from entry point [ input_var : TSL2=10]

1- At 1hour time frame price close above close above or below Linear wightetd moving averages 4 and 8. With condition for direction as follow
@ 1HR TF
LinearWMA 4 & 8 crossing
WMA8 > WMA16 = buy trade zone 
WMA 8 < WMA16 = sell trade zone

2- at 1 minute time frame, decide the entry signal based on 4 conditions
A- Wait for price to make multiple tops/bottoms (2+) 
B- wait for price to cross and close the 50 LWMA in the direction of the 1 hour time frame defined trade zone
B- confirmation using a deliverance in RSI (7) , MACD, Stoch for multiple tops/bottoms with following conditions 
C- ENTRY POINT: after divergence confirmation and cross-close WMA 50 
		
@ 1M TF
(LookBack) Crosses and close EMA 50 
(Look back) Detect clear divergence on RSI && MACD &| STOCH
(Trigger) Wait to cross EMA50 and close + Divergence

3- General conditions 
There should be an option to limit trading in defined hours or to run all the time
[ input_var : limite_hours=False]
[ input_var : trade_hours_start=10:00]
[ input_var : trade_hours_stop=10:00]



