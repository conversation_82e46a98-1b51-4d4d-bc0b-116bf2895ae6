# MQL5 Divergence Trading System

This repository contains two MQL5 files implementing a sophisticated divergence-based trading strategy:

## Files Created

1. **DivergenceSignalIndicator.mq5** - Custom indicator that displays entry signals on the chart
2. **DivergenceSignalEA.mq5** - Expert Advisor that automatically trades based on the strategy

## Strategy Overview

### Multi-Timeframe Analysis
- **1-Hour Timeframe**: Uses Linear Weighted Moving Averages (WMA4, WMA8, WMA16) to determine overall trend direction
- **1-Minute Timeframe**: Analyzes entry signals using WMA50, RSI(7), MACD, and Stochastic indicators

### Entry Conditions
1. **Trend Direction (1H)**: 
   - Buy Zone: WMA8 > WMA16
   - Sell Zone: WMA8 < WMA16

2. **Entry Signal (1M)**:
   - Multiple tops/bottoms formation (2+)
   - Price crosses and closes above/below WMA50 in trend direction
   - Divergence confirmation in at least 2 of 3 indicators (RSI, MACD, Stochastic)

### Exit Strategy
- **Target**: 7-20 pips minimum
- **Exit Condition**: Price crosses and closes back through WMA50 in opposite direction (if profit > min_target)
- **Stop Loss**: Recent multiple bottoms/tops with divergence + 3 pips

### Trailing Stop Rules
1. **TSL1 (5 pips)**: Move stop loss to breakeven (entry price)
2. **TSL2 (10 pips)**: Move stop loss to 3 pips profit from entry

## Input Parameters

### Common Parameters
- `min_target` (default: 7) - Minimum target in pips
- `max_target` (default: 20) - Maximum target in pips  
- `TSL1` (default: 5) - First trailing stop level in pips
- `TSL2` (default: 10) - Second trailing stop level in pips
- `limite_hours` (default: false) - Enable/disable trading hour restrictions
- `trade_hours_start` (default: "10:00") - Trading start time
- `trade_hours_stop` (default: "22:00") - Trading stop time

### EA-Specific Parameters
- `lot_size` (default: 0.1) - Position size for trades
- `magic_number` (default: 12345) - Unique identifier for EA trades

## Features

### Indicator Features
- **Visual Signals**: Displays buy/sell arrows on chart
- **Divergence Lines**: Draws trend lines showing divergence patterns
- **Historical Analysis**: Shows signals from previous 2 days when added to chart
- **Real-time Detection**: Identifies new signals as they occur
- **Clean Removal**: Automatically removes all drawings when indicator is removed

### Expert Advisor Features
- **Automated Trading**: Opens and closes positions based on strategy
- **Risk Management**: Implements stop loss and take profit levels
- **Trailing Stops**: Automatically adjusts stop loss as position moves in profit
- **Position Management**: Monitors and manages existing trades
- **Trading Hours**: Optional time-based trading restrictions

## Technical Implementation

### Indicators Used
- **Linear Weighted Moving Averages**: WMA4, WMA8, WMA16 (1H), WMA50 (1M)
- **RSI**: 7-period Relative Strength Index
- **MACD**: 12, 26, 9 Moving Average Convergence Divergence
- **Stochastic**: 5, 3, 3 Stochastic Oscillator

### Divergence Detection
- **Bullish Divergence**: Price makes lower lows while indicators make higher lows
- **Bearish Divergence**: Price makes higher highs while indicators make lower highs
- **Confirmation**: Requires at least 2 out of 3 indicators to show divergence

## Installation and Usage

1. Copy the `.mq5` files to your MetaTrader 5 `MQL5/Indicators/` or `MQL5/Experts/` folder
2. Compile the files in MetaEditor
3. For the indicator: Drag to chart and configure input parameters
4. For the EA: Attach to chart, enable auto-trading, and configure parameters

## Testing Recommendations

Before live trading:
1. Test the indicator on historical data to verify signal accuracy
2. Backtest the EA on different timeframes and market conditions
3. Forward test on demo account to validate real-time performance
4. Optimize parameters for specific currency pairs and market conditions

## Risk Warning

This trading system involves substantial risk. Past performance does not guarantee future results. Always test thoroughly on demo accounts before live trading and never risk more than you can afford to lose.
