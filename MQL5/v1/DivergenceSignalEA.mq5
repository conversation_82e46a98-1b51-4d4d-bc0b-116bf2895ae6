//+------------------------------------------------------------------+
//|                                        DivergenceSignalEA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>

//--- Input parameters
input int      min_target = 7;           // Minimum target in pips
input int      max_target = 20;          // Maximum target in pips
input int      TSL1 = 5;                 // First trailing stop level in pips
input int      TSL2 = 10;                // Second trailing stop level in pips
input bool     limite_hours = false;     // Limit trading hours
input string   trade_hours_start = "10:00"; // Trading start time
input string   trade_hours_stop = "22:00";  // Trading stop time
input double   lot_size = 0.1;           // Lot size for trades
input int      magic_number = 12345;     // Magic number for trades

//--- Global variables
CTrade trade;
int wma4_handle_h1, wma8_handle_h1, wma16_handle_h1;
int wma50_handle_m1;
int rsi_handle_m1, macd_handle_m1, stoch_handle_m1;

double wma4_h1[], wma8_h1[], wma16_h1[];
double wma50_m1[];
double rsi_m1[], macd_main[], macd_signal[], stoch_main[], stoch_signal[];

double entry_price = 0;
datetime last_signal_time = 0;
bool position_moved_to_breakeven = false;
bool position_moved_to_tsl2 = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set trade parameters
    trade.SetExpertMagicNumber(magic_number);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    //--- Initialize indicator handles for 1H timeframe
    wma4_handle_h1 = iMA(_Symbol, PERIOD_H1, 4, 0, MODE_LWMA, PRICE_CLOSE);
    wma8_handle_h1 = iMA(_Symbol, PERIOD_H1, 8, 0, MODE_LWMA, PRICE_CLOSE);
    wma16_handle_h1 = iMA(_Symbol, PERIOD_H1, 16, 0, MODE_LWMA, PRICE_CLOSE);
    
    //--- Initialize indicator handles for 1M timeframe
    wma50_handle_m1 = iMA(_Symbol, PERIOD_M1, 50, 0, MODE_LWMA, PRICE_CLOSE);
    rsi_handle_m1 = iRSI(_Symbol, PERIOD_M1, 7, PRICE_CLOSE);
    macd_handle_m1 = iMACD(_Symbol, PERIOD_M1, 12, 26, 9, PRICE_CLOSE);
    stoch_handle_m1 = iStochastic(_Symbol, PERIOD_M1, 5, 3, 3, MODE_SMA, STO_LOWHIGH);
    
    //--- Check if handles are valid
    if(wma4_handle_h1 == INVALID_HANDLE || wma8_handle_h1 == INVALID_HANDLE || 
       wma16_handle_h1 == INVALID_HANDLE || wma50_handle_m1 == INVALID_HANDLE ||
       rsi_handle_m1 == INVALID_HANDLE || macd_handle_m1 == INVALID_HANDLE ||
       stoch_handle_m1 == INVALID_HANDLE)
    {
        Print("Error creating indicator handles");
        return(INIT_FAILED);
    }
    
    //--- Resize arrays
    ArraySetAsSeries(wma4_h1, true);
    ArraySetAsSeries(wma8_h1, true);
    ArraySetAsSeries(wma16_h1, true);
    ArraySetAsSeries(wma50_m1, true);
    ArraySetAsSeries(rsi_m1, true);
    ArraySetAsSeries(macd_main, true);
    ArraySetAsSeries(macd_signal, true);
    ArraySetAsSeries(stoch_main, true);
    ArraySetAsSeries(stoch_signal, true);
    
    Print("DivergenceSignalEA initialized successfully");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handles
    if(wma4_handle_h1 != INVALID_HANDLE) IndicatorRelease(wma4_handle_h1);
    if(wma8_handle_h1 != INVALID_HANDLE) IndicatorRelease(wma8_handle_h1);
    if(wma16_handle_h1 != INVALID_HANDLE) IndicatorRelease(wma16_handle_h1);
    if(wma50_handle_m1 != INVALID_HANDLE) IndicatorRelease(wma50_handle_m1);
    if(rsi_handle_m1 != INVALID_HANDLE) IndicatorRelease(rsi_handle_m1);
    if(macd_handle_m1 != INVALID_HANDLE) IndicatorRelease(macd_handle_m1);
    if(stoch_handle_m1 != INVALID_HANDLE) IndicatorRelease(stoch_handle_m1);
    
    Print("DivergenceSignalEA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check for new signals
    CheckForSignals();
    
    //--- Manage existing positions
    ManagePositions();
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckForSignals()
{
    //--- Check if trading hours are limited
    if(limite_hours && !IsWithinTradingHours())
        return;
        
    //--- Don't open new position if one already exists
    if(PositionsTotal() > 0)
        return;
        
    //--- Check for signal conditions
    if(!CheckSignalConditions(0))
        return;
        
    //--- Prevent multiple signals in short time
    if(TimeCurrent() - last_signal_time < 60) // 1 minute minimum between signals
        return;
        
    int signal_type = GetSignalType(0);
    if(signal_type == 0)
        return;
        
    //--- Execute trade
    if(signal_type > 0)
        OpenBuyPosition();
    else
        OpenSellPosition();
        
    last_signal_time = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Open buy position                                                |
//+------------------------------------------------------------------+
void OpenBuyPosition()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = CalculateStopLoss(true);
    double tp = ask + max_target * Point() * 10;
    
    if(trade.Buy(lot_size, _Symbol, ask, sl, tp, "Divergence Buy Signal"))
    {
        entry_price = ask;
        position_moved_to_breakeven = false;
        position_moved_to_tsl2 = false;
        Print("Buy position opened at ", ask, " SL: ", sl, " TP: ", tp);
    }
    else
    {
        Print("Failed to open buy position. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Open sell position                                               |
//+------------------------------------------------------------------+
void OpenSellPosition()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = CalculateStopLoss(false);
    double tp = bid - max_target * Point() * 10;
    
    if(trade.Sell(lot_size, _Symbol, bid, sl, tp, "Divergence Sell Signal"))
    {
        entry_price = bid;
        position_moved_to_breakeven = false;
        position_moved_to_tsl2 = false;
        Print("Sell position opened at ", bid, " SL: ", sl, " TP: ", tp);
    }
    else
    {
        Print("Failed to open sell position. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Calculate stop loss based on recent bottoms/tops with divergence |
//+------------------------------------------------------------------+
double CalculateStopLoss(bool is_buy)
{
    double sl_level = 0;
    int lookback = 50;
    
    if(is_buy)
    {
        // Find recent low with divergence + 3 pips
        double lows[];
        if(CopyLow(_Symbol, PERIOD_M1, 0, lookback, lows) >= lookback)
        {
            ArraySetAsSeries(lows, true);
            double recent_low = lows[ArrayMinimum(lows, 0, lookback)];
            sl_level = recent_low - 3 * Point() * 10;
        }
    }
    else
    {
        // Find recent high with divergence + 3 pips
        double highs[];
        if(CopyHigh(_Symbol, PERIOD_M1, 0, lookback, highs) >= lookback)
        {
            ArraySetAsSeries(highs, true);
            double recent_high = highs[ArrayMaximum(highs, 0, lookback)];
            sl_level = recent_high + 3 * Point() * 10;
        }
    }
    
    return sl_level;
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManagePositions()
{
    if(PositionsTotal() == 0)
        return;
        
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == magic_number)
        {
            ManageTrailingStop();
            CheckExitConditions();
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Manage trailing stop                                             |
//+------------------------------------------------------------------+
void ManageTrailingStop()
{
    double current_price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? 
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    double position_profit_pips = 0;
    
    if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
    {
        position_profit_pips = (current_price - entry_price) / (Point() * 10);
    }
    else
    {
        position_profit_pips = (entry_price - current_price) / (Point() * 10);
    }
    
    // Move to breakeven when profit reaches TSL1 (5 pips)
    if(!position_moved_to_breakeven && position_profit_pips >= TSL1)
    {
        if(trade.PositionModify(_Symbol, entry_price, PositionGetDouble(POSITION_TP)))
        {
            position_moved_to_breakeven = true;
            Print("Stop loss moved to breakeven at ", entry_price);
        }
    }
    
    // Move to 3 pips profit when profit reaches TSL2 (10 pips)
    if(!position_moved_to_tsl2 && position_profit_pips >= TSL2)
    {
        double new_sl = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? 
                       entry_price + 3 * Point() * 10 : 
                       entry_price - 3 * Point() * 10;
                       
        if(trade.PositionModify(_Symbol, new_sl, PositionGetDouble(POSITION_TP)))
        {
            position_moved_to_tsl2 = true;
            Print("Stop loss moved to 3 pips profit at ", new_sl);
        }
    }
}

//+------------------------------------------------------------------+
//| Check exit conditions                                            |
//+------------------------------------------------------------------+
void CheckExitConditions()
{
    // Get current WMA50 value
    if(CopyBuffer(wma50_handle_m1, 0, 0, 2, wma50_m1) < 2)
        return;

    double close_prices[];
    if(CopyClose(_Symbol, PERIOD_M1, 0, 2, close_prices) < 2)
        return;

    ArraySetAsSeries(close_prices, true);

    double current_price = close_prices[0];
    double position_profit_pips = 0;

    if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
    {
        position_profit_pips = (current_price - entry_price) / (Point() * 10);

        // Exit if price crosses and closes below WMA50 and profit > min_target
        if(position_profit_pips >= min_target &&
           close_prices[1] >= wma50_m1[1] && current_price < wma50_m1[0])
        {
            trade.PositionClose(_Symbol);
            Print("Buy position closed due to WMA50 cross below with profit: ", position_profit_pips, " pips");
        }
    }
    else
    {
        position_profit_pips = (entry_price - current_price) / (Point() * 10);

        // Exit if price crosses and closes above WMA50 and profit > min_target
        if(position_profit_pips >= min_target &&
           close_prices[1] <= wma50_m1[1] && current_price > wma50_m1[0])
        {
            trade.PositionClose(_Symbol);
            Print("Sell position closed due to WMA50 cross above with profit: ", position_profit_pips, " pips");
        }
    }
}

//+------------------------------------------------------------------+
//| Check if current time is within trading hours                   |
//+------------------------------------------------------------------+
bool IsWithinTradingHours()
{
    if(!limite_hours) return true;

    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    int current_minutes = dt.hour * 60 + dt.min;

    // Parse start and stop times
    string start_parts[];
    string stop_parts[];
    StringSplit(trade_hours_start, ':', start_parts);
    StringSplit(trade_hours_stop, ':', stop_parts);

    int start_minutes = (int)start_parts[0] * 60 + (int)start_parts[1];
    int stop_minutes = (int)stop_parts[0] * 60 + (int)stop_parts[1];

    if(start_minutes <= stop_minutes)
    {
        return (current_minutes >= start_minutes && current_minutes <= stop_minutes);
    }
    else
    {
        return (current_minutes >= start_minutes || current_minutes <= stop_minutes);
    }
}

//+------------------------------------------------------------------+
//| Check signal conditions for given bar                           |
//+------------------------------------------------------------------+
bool CheckSignalConditions(int bar_index)
{
    // Get 1H timeframe data for trend direction
    if(!GetH1TrendDirection(bar_index))
        return false;

    // Check for multiple tops/bottoms
    if(!HasMultipleTopsBottoms(bar_index))
        return false;

    // Check WMA50 cross
    if(!HasWMA50Cross(bar_index))
        return false;

    // Check for divergence
    if(!HasDivergence(bar_index))
        return false;

    return true;
}

//+------------------------------------------------------------------+
//| Get 1H trend direction                                          |
//+------------------------------------------------------------------+
bool GetH1TrendDirection(int bar_index)
{
    // Get corresponding H1 bar
    datetime m1_time = iTime(_Symbol, PERIOD_M1, bar_index);
    int h1_bar = iBarShift(_Symbol, PERIOD_H1, m1_time);

    // Get WMA values
    if(CopyBuffer(wma8_handle_h1, 0, h1_bar, 2, wma8_h1) < 2 ||
       CopyBuffer(wma16_handle_h1, 0, h1_bar, 2, wma16_h1) < 2)
        return false;

    // Check trend direction: WMA8 > WMA16 = Buy zone, WMA8 < WMA16 = Sell zone
    return (MathAbs(wma8_h1[0] - wma16_h1[0]) > Point() * 2); // Minimum 2 points difference
}

//+------------------------------------------------------------------+
//| Check for multiple tops/bottoms                                 |
//+------------------------------------------------------------------+
bool HasMultipleTopsBottoms(int bar_index)
{
    int lookback = 20; // Look back 20 bars for tops/bottoms
    double highs[], lows[];

    if(CopyHigh(_Symbol, PERIOD_M1, bar_index, lookback, highs) < lookback ||
       CopyLow(_Symbol, PERIOD_M1, bar_index, lookback, lows) < lookback)
        return false;

    ArraySetAsSeries(highs, true);
    ArraySetAsSeries(lows, true);

    // Count significant tops and bottoms
    int tops = 0, bottoms = 0;

    for(int i = 2; i < lookback - 2; i++)
    {
        // Check for top
        if(highs[i] > highs[i-1] && highs[i] > highs[i+1] &&
           highs[i] > highs[i-2] && highs[i] > highs[i+2])
        {
            tops++;
        }

        // Check for bottom
        if(lows[i] < lows[i-1] && lows[i] < lows[i+1] &&
           lows[i] < lows[i-2] && lows[i] < lows[i+2])
        {
            bottoms++;
        }
    }

    return (tops >= 2 || bottoms >= 2);
}

//+------------------------------------------------------------------+
//| Check WMA50 cross                                               |
//+------------------------------------------------------------------+
bool HasWMA50Cross(int bar_index)
{
    if(CopyBuffer(wma50_handle_m1, 0, bar_index, 3, wma50_m1) < 3)
        return false;

    double close_prices[];
    if(CopyClose(_Symbol, PERIOD_M1, bar_index, 3, close_prices) < 3)
        return false;

    ArraySetAsSeries(close_prices, true);

    // Check if price crossed and closed above/below WMA50
    bool cross_up = (close_prices[1] <= wma50_m1[1] && close_prices[0] > wma50_m1[0]);
    bool cross_down = (close_prices[1] >= wma50_m1[1] && close_prices[0] < wma50_m1[0]);

    return (cross_up || cross_down);
}

//+------------------------------------------------------------------+
//| Check for divergence in RSI, MACD, and Stochastic             |
//+------------------------------------------------------------------+
bool HasDivergence(int bar_index)
{
    int lookback = 20;

    // Get indicator values
    if(CopyBuffer(rsi_handle_m1, 0, bar_index, lookback, rsi_m1) < lookback ||
       CopyBuffer(macd_handle_m1, 0, bar_index, lookback, macd_main) < lookback ||
       CopyBuffer(stoch_handle_m1, 0, bar_index, lookback, stoch_main) < lookback)
        return false;

    double highs[], lows[];
    if(CopyHigh(_Symbol, PERIOD_M1, bar_index, lookback, highs) < lookback ||
       CopyLow(_Symbol, PERIOD_M1, bar_index, lookback, lows) < lookback)
        return false;

    ArraySetAsSeries(highs, true);
    ArraySetAsSeries(lows, true);

    // Check for bullish divergence (price makes lower lows, indicators make higher lows)
    bool rsi_bull_div = CheckBullishDivergence(lows, rsi_m1, lookback);
    bool macd_bull_div = CheckBullishDivergence(lows, macd_main, lookback);
    bool stoch_bull_div = CheckBullishDivergence(lows, stoch_main, lookback);

    // Check for bearish divergence (price makes higher highs, indicators make lower highs)
    bool rsi_bear_div = CheckBearishDivergence(highs, rsi_m1, lookback);
    bool macd_bear_div = CheckBearishDivergence(highs, macd_main, lookback);
    bool stoch_bear_div = CheckBearishDivergence(highs, stoch_main, lookback);

    // Require at least 2 indicators to show divergence
    int bull_count = (rsi_bull_div ? 1 : 0) + (macd_bull_div ? 1 : 0) + (stoch_bull_div ? 1 : 0);
    int bear_count = (rsi_bear_div ? 1 : 0) + (macd_bear_div ? 1 : 0) + (stoch_bear_div ? 1 : 0);

    return (bull_count >= 2 || bear_count >= 2);
}

//+------------------------------------------------------------------+
//| Check bullish divergence                                        |
//+------------------------------------------------------------------+
bool CheckBullishDivergence(double &price_array[], double &indicator_array[], int size)
{
    // Find two recent lows in price
    int low1_idx = -1, low2_idx = -1;
    double low1_price = DBL_MAX, low2_price = DBL_MAX;

    for(int i = 2; i < size - 2; i++)
    {
        if(price_array[i] < price_array[i-1] && price_array[i] < price_array[i+1] &&
           price_array[i] < price_array[i-2] && price_array[i] < price_array[i+2])
        {
            if(low1_idx == -1 || price_array[i] < low1_price)
            {
                low2_idx = low1_idx;
                low2_price = low1_price;
                low1_idx = i;
                low1_price = price_array[i];
            }
            else if(low2_idx == -1 || price_array[i] < low2_price)
            {
                low2_idx = i;
                low2_price = price_array[i];
            }
        }
    }

    if(low1_idx == -1 || low2_idx == -1) return false;

    // Check if indicator shows higher low when price shows lower low
    return (low1_price < low2_price && indicator_array[low1_idx] > indicator_array[low2_idx]);
}

//+------------------------------------------------------------------+
//| Check bearish divergence                                        |
//+------------------------------------------------------------------+
bool CheckBearishDivergence(double &price_array[], double &indicator_array[], int size)
{
    // Find two recent highs in price
    int high1_idx = -1, high2_idx = -1;
    double high1_price = -DBL_MAX, high2_price = -DBL_MAX;

    for(int i = 2; i < size - 2; i++)
    {
        if(price_array[i] > price_array[i-1] && price_array[i] > price_array[i+1] &&
           price_array[i] > price_array[i-2] && price_array[i] > price_array[i+2])
        {
            if(high1_idx == -1 || price_array[i] > high1_price)
            {
                high2_idx = high1_idx;
                high2_price = high1_price;
                high1_idx = i;
                high1_price = price_array[i];
            }
            else if(high2_idx == -1 || price_array[i] > high2_price)
            {
                high2_idx = i;
                high2_price = price_array[i];
            }
        }
    }

    if(high1_idx == -1 || high2_idx == -1) return false;

    // Check if indicator shows lower high when price shows higher high
    return (high1_price > high2_price && indicator_array[high1_idx] < indicator_array[high2_idx]);
}

//+------------------------------------------------------------------+
//| Get signal type (1 = Buy, -1 = Sell, 0 = No signal)           |
//+------------------------------------------------------------------+
int GetSignalType(int bar_index)
{
    // Get H1 trend direction
    datetime m1_time = iTime(_Symbol, PERIOD_M1, bar_index);
    int h1_bar = iBarShift(_Symbol, PERIOD_H1, m1_time);

    if(CopyBuffer(wma8_handle_h1, 0, h1_bar, 1, wma8_h1) < 1 ||
       CopyBuffer(wma16_handle_h1, 0, h1_bar, 1, wma16_h1) < 1)
        return 0;

    // Get WMA50 cross direction
    if(CopyBuffer(wma50_handle_m1, 0, bar_index, 2, wma50_m1) < 2)
        return 0;

    double close_prices[];
    if(CopyClose(_Symbol, PERIOD_M1, bar_index, 2, close_prices) < 2)
        return 0;

    ArraySetAsSeries(close_prices, true);

    bool cross_up = (close_prices[1] <= wma50_m1[1] && close_prices[0] > wma50_m1[0]);
    bool cross_down = (close_prices[1] >= wma50_m1[1] && close_prices[0] < wma50_m1[0]);

    // Buy signal: H1 uptrend + M1 cross up + bullish divergence
    if(wma8_h1[0] > wma16_h1[0] && cross_up)
        return 1;

    // Sell signal: H1 downtrend + M1 cross down + bearish divergence
    if(wma8_h1[0] < wma16_h1[0] && cross_down)
        return -1;

    return 0;
}
