//@version=6
strategy("Divergence Signal Strategy v2", shorttitle="DivSignal", overlay=true, 
         default_qty_type=strategy.percent_of_equity, default_qty_value=10,
         commission_type=strategy.commission.percent, commission_value=0.1)

// Input Parameters
min_target = input.int(7, "Minimum Target (pips)", minval=1)
max_target = input.int(20, "Maximum Target (pips)", minval=1)
TSL1 = input.int(5, "First Trailing Stop (pips)", minval=1)
TSL2 = input.int(10, "Second Trailing Stop (pips)", minval=1)
sl_pips = input.int(3, "Stop Loss (pips)", minval=1)

// v2 Enhanced parameters
max_consecutive_losses = input.int(3, "Max Consecutive Losses", minval=1)
h1_200_check = input.bool(true, "Check 1H WMA200")
max_sl_pips = input.int(10, "Max SL Distance (pips)", minval=1)
no_trade_after_win = input.bool(true, "No Trade After Win Same Hour")
candles_after_win = input.int(10, "Candles After Win", minval=1)

// Trading hours
limite_hours = input.bool(false, "Limit Trading Hours")
trade_hours_start = input.session("1000-2200", "Trading Hours")

// Moving Averages - 1H timeframe
wma4_h1 = request.security(syminfo.tickerid, "1H", ta.wma(close, 4))
wma8_h1 = request.security(syminfo.tickerid, "1H", ta.wma(close, 8))
wma16_h1 = request.security(syminfo.tickerid, "1H", ta.wma(close, 16))
wma200_h1 = request.security(syminfo.tickerid, "1H", ta.wma(close, 200))
h1_close = request.security(syminfo.tickerid, "1H", close)

// Moving Averages - 1M timeframe
wma50_m1 = ta.wma(close, 50)
wma200_m1 = ta.wma(close, 200)

// Oscillators - 1M timeframe
rsi_m1 = ta.rsi(close, 7)
macd_line, macd_signal, macd_hist := ta.macd(close, 12, 26, 9)
stoch_k, stoch_d := ta.stoch(close, high, low, 5)

// Global variables
var float entry_price = na
var bool position_moved_to_breakeven = false
var bool position_moved_to_tsl2 = false
var int consecutive_losses = 0
var bool trading_disabled = false
var int last_win_bar = 0

// Helper Functions

// Check if within trading hours
is_within_trading_hours() =>
    if limite_hours
        time(timeframe.period, trade_hours_start)
    else
        true

// Get 1H trend direction
get_h1_trend_direction() =>
    trend_strength = math.abs(wma8_h1 - wma16_h1)
    min_strength = syminfo.mintick * 2
    trend_strength > min_strength

// Check 1H WMA200 condition
check_h1_wma200() =>
    if not h1_200_check
        true
    else
        is_uptrend = wma8_h1 > wma16_h1
        is_uptrend ? h1_close > wma200_h1 : h1_close < wma200_h1

// Check 1M WMA200 condition
check_m1_wma200() =>
    is_uptrend = wma8_h1 > wma16_h1
    is_uptrend ? close > wma200_m1 : close < wma200_m1

// Enhanced WMA50 cross with 3-candle confirmation
has_enhanced_wma50_cross() =>
    // Check for cross 3 bars ago
    cross_up_3 = close[3] <= wma50_m1[3] and close[2] > wma50_m1[2]
    cross_down_3 = close[3] >= wma50_m1[3] and close[2] < wma50_m1[2]
    
    // Check 3-candle confirmation
    if cross_up_3
        close[2] > wma50_m1[2] and close[1] > wma50_m1[1] and close > wma50_m1
    else if cross_down_3
        close[2] < wma50_m1[2] and close[1] < wma50_m1[1] and close < wma50_m1
    else
        false

// Check for multiple tops/bottoms
has_multiple_tops_bottoms() =>
    lookback = 20
    tops = 0
    bottoms = 0
    
    for i = 2 to lookback - 3
        // Check for top
        if high[i] > high[i-1] and high[i] > high[i+1] and high[i] > high[i-2] and high[i] > high[i+2]
            tops += 1
        
        // Check for bottom
        if low[i] < low[i-1] and low[i] < low[i+1] and low[i] < low[i-2] and low[i] < low[i+2]
            bottoms += 1
    
    tops >= 2 or bottoms >= 2

// Check bullish divergence
check_bullish_divergence(price_array, indicator_array, size) =>
    var int low1_idx = na
    var int low2_idx = na
    var float low1_price = na
    var float low2_price = na
    
    low1_idx := na
    low2_idx := na
    low1_price := 999999.0
    low2_price := 999999.0
    
    for i = 2 to size - 3
        price_val = array.get(price_array, i)
        if price_val < array.get(price_array, i-1) and price_val < array.get(price_array, i+1) and 
           price_val < array.get(price_array, i-2) and price_val < array.get(price_array, i+2)
            if na(low1_idx) or price_val < low1_price
                low2_idx := low1_idx
                low2_price := low1_price
                low1_idx := i
                low1_price := price_val
            else if na(low2_idx) or price_val < low2_price
                low2_idx := i
                low2_price := price_val
    
    if not na(low1_idx) and not na(low2_idx)
        low1_price < low2_price and array.get(indicator_array, low1_idx) > array.get(indicator_array, low2_idx)
    else
        false

// Check bearish divergence
check_bearish_divergence(price_array, indicator_array, size) =>
    var int high1_idx = na
    var int high2_idx = na
    var float high1_price = na
    var float high2_price = na
    
    high1_idx := na
    high2_idx := na
    high1_price := -999999.0
    high2_price := -999999.0
    
    for i = 2 to size - 3
        price_val = array.get(price_array, i)
        if price_val > array.get(price_array, i-1) and price_val > array.get(price_array, i+1) and 
           price_val > array.get(price_array, i-2) and price_val > array.get(price_array, i+2)
            if na(high1_idx) or price_val > high1_price
                high2_idx := high1_idx
                high2_price := high1_price
                high1_idx := i
                high1_price := price_val
            else if na(high2_idx) or price_val > high2_price
                high2_idx := i
                high2_price := price_val
    
    if not na(high1_idx) and not na(high2_idx)
        high1_price > high2_price and array.get(indicator_array, high1_idx) < array.get(indicator_array, high2_idx)
    else
        false

// Check for divergence
has_divergence() =>
    lookback = 20
    
    // Create arrays for price and indicators
    highs_array = array.new<float>(lookback)
    lows_array = array.new<float>(lookback)
    rsi_array = array.new<float>(lookback)
    macd_array = array.new<float>(lookback)
    stoch_array = array.new<float>(lookback)
    
    // Fill arrays
    for i = 0 to lookback - 1
        array.set(highs_array, i, high[i])
        array.set(lows_array, i, low[i])
        array.set(rsi_array, i, rsi_m1[i])
        array.set(macd_array, i, macd_line[i])
        array.set(stoch_array, i, stoch_k[i])
    
    // Check divergences
    rsi_bull_div = check_bullish_divergence(lows_array, rsi_array, lookback)
    macd_bull_div = check_bullish_divergence(lows_array, macd_array, lookback)
    stoch_bull_div = check_bullish_divergence(lows_array, stoch_array, lookback)
    
    rsi_bear_div = check_bearish_divergence(highs_array, rsi_array, lookback)
    macd_bear_div = check_bearish_divergence(highs_array, macd_array, lookback)
    stoch_bear_div = check_bearish_divergence(highs_array, stoch_array, lookback)
    
    // Require at least 2 indicators to show divergence
    bull_count = (rsi_bull_div ? 1 : 0) + (macd_bull_div ? 1 : 0) + (stoch_bull_div ? 1 : 0)
    bear_count = (rsi_bear_div ? 1 : 0) + (macd_bear_div ? 1 : 0) + (stoch_bear_div ? 1 : 0)
    
    bull_count >= 2 or bear_count >= 2

// Main signal conditions
check_signal_conditions() =>
    get_h1_trend_direction() and 
    check_h1_wma200() and 
    has_multiple_tops_bottoms() and 
    has_enhanced_wma50_cross() and 
    check_m1_wma200() and 
    has_divergence()

// Get signal type
get_signal_type() =>
    is_uptrend = wma8_h1 > wma16_h1
    cross_up = close[3] <= wma50_m1[3] and close[2] > wma50_m1[2] and 
               close[2] > wma50_m1[2] and close[1] > wma50_m1[1] and close > wma50_m1
    cross_down = close[3] >= wma50_m1[3] and close[2] < wma50_m1[2] and 
                 close[2] < wma50_m1[2] and close[1] < wma50_m1[1] and close < wma50_m1
    
    if is_uptrend and cross_up
        1  // Buy signal
    else if not is_uptrend and cross_down
        -1  // Sell signal
    else
        0  // No signal

// Strategy Logic
signal_conditions = check_signal_conditions()
signal_type = get_signal_type()

// Check trading conditions
can_trade = not trading_disabled and is_within_trading_hours()

// Check no-trade-after-win condition
bars_since_win = bar_index - last_win_bar
can_trade_after_win = not no_trade_after_win or not (last_win_bar > 0 and bars_since_win < candles_after_win)

// Entry conditions
long_condition = signal_conditions and signal_type == 1 and can_trade and can_trade_after_win and strategy.position_size == 0
short_condition = signal_conditions and signal_type == -1 and can_trade and can_trade_after_win and strategy.position_size == 0

// Calculate stop loss distance
current_price = close
sl_distance_long = sl_pips * syminfo.mintick * 10
sl_distance_short = sl_pips * syminfo.mintick * 10

// Check maximum SL distance
sl_distance_pips_long = sl_distance_long / (syminfo.mintick * 10)
sl_distance_pips_short = sl_distance_short / (syminfo.mintick * 10)

valid_sl_long = sl_distance_pips_long <= max_sl_pips
valid_sl_short = sl_distance_pips_short <= max_sl_pips

// Entry orders
if long_condition and valid_sl_long
    entry_price := close
    sl_price = close - sl_distance_long
    tp_price = close + max_target * syminfo.mintick * 10
    strategy.entry("Long", strategy.long, comment="Divergence Buy")
    strategy.exit("Long Exit", "Long", stop=sl_price, limit=tp_price)
    position_moved_to_breakeven := false
    position_moved_to_tsl2 := false

if short_condition and valid_sl_short
    entry_price := close
    sl_price = close + sl_distance_short
    tp_price = close - max_target * syminfo.mintick * 10
    strategy.entry("Short", strategy.short, comment="Divergence Sell")
    strategy.exit("Short Exit", "Short", stop=sl_price, limit=tp_price)
    position_moved_to_breakeven := false
    position_moved_to_tsl2 := false

// Trailing Stop Logic
if strategy.position_size > 0  // Long position
    profit_pips = (close - entry_price) / (syminfo.mintick * 10)

    // Move to breakeven at TSL1
    if not position_moved_to_breakeven and profit_pips >= TSL1
        strategy.exit("Long Exit", "Long", stop=entry_price, limit=strategy.position_avg_price + max_target * syminfo.mintick * 10)
        position_moved_to_breakeven := true

    // Move to 3 pips profit at TSL2
    if not position_moved_to_tsl2 and profit_pips >= TSL2
        new_sl = entry_price + 3 * syminfo.mintick * 10
        strategy.exit("Long Exit", "Long", stop=new_sl, limit=strategy.position_avg_price + max_target * syminfo.mintick * 10)
        position_moved_to_tsl2 := true

    // Exit on WMA50 cross
    if profit_pips >= min_target and close[1] >= wma50_m1[1] and close < wma50_m1
        strategy.close("Long", comment="WMA50 Cross Exit")

if strategy.position_size < 0  // Short position
    profit_pips = (entry_price - close) / (syminfo.mintick * 10)

    // Move to breakeven at TSL1
    if not position_moved_to_breakeven and profit_pips >= TSL1
        strategy.exit("Short Exit", "Short", stop=entry_price, limit=strategy.position_avg_price - max_target * syminfo.mintick * 10)
        position_moved_to_breakeven := true

    // Move to 3 pips profit at TSL2
    if not position_moved_to_tsl2 and profit_pips >= TSL2
        new_sl = entry_price - 3 * syminfo.mintick * 10
        strategy.exit("Short Exit", "Short", stop=new_sl, limit=strategy.position_avg_price - max_target * syminfo.mintick * 10)
        position_moved_to_tsl2 := true

    // Exit on WMA50 cross
    if profit_pips >= min_target and close[1] <= wma50_m1[1] and close > wma50_m1
        strategy.close("Short", comment="WMA50 Cross Exit")

// Track consecutive losses
if strategy.closedtrades > 0
    last_trade_profit = strategy.closedtrades.profit(strategy.closedtrades - 1)
    if last_trade_profit > 0
        consecutive_losses := 0
        last_win_bar := bar_index
    else
        consecutive_losses += 1
        if consecutive_losses >= max_consecutive_losses
            trading_disabled := true

// Visualizations
// Plot moving averages
plot(wma50_m1, "WMA50", color=color.blue, linewidth=2)
plot(wma200_m1, "WMA200", color=color.orange, linewidth=1)

// Plot entry signals
plotshape(long_condition, "Buy Signal", shape.triangleup, location.belowbar, color.lime, size=size.normal)
plotshape(short_condition, "Sell Signal", shape.triangledown, location.abovebar, color.red, size=size.normal)

// Plot stop loss and take profit levels
var line sl_line = na
var line tp_line = na

if strategy.position_size != 0 and na(sl_line)
    if strategy.position_size > 0
        sl_level = entry_price - sl_pips * syminfo.mintick * 10
        tp_level = entry_price + max_target * syminfo.mintick * 10
    else
        sl_level = entry_price + sl_pips * syminfo.mintick * 10
        tp_level = entry_price - max_target * syminfo.mintick * 10

    sl_line := line.new(bar_index, sl_level, bar_index + 10, sl_level, color=color.red, width=2, style=line.style_dashed)
    tp_line := line.new(bar_index, tp_level, bar_index + 10, tp_level, color=color.green, width=2, style=line.style_dashed)

if strategy.position_size == 0 and not na(sl_line)
    line.delete(sl_line)
    line.delete(tp_line)
    sl_line := na
    tp_line := na

// Alerts
alertcondition(long_condition, "Long Signal", "Divergence Buy Signal Detected")
alertcondition(short_condition, "Short Signal", "Divergence Sell Signal Detected")
alertcondition(trading_disabled, "Trading Disabled", "Trading disabled after consecutive losses")

// Display information
var table info_table = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "Consecutive Losses", text_color=color.black)
    table.cell(info_table, 1, 0, str.tostring(consecutive_losses), text_color=color.black)
    table.cell(info_table, 0, 1, "Trading Status", text_color=color.black)
    table.cell(info_table, 1, 1, trading_disabled ? "DISABLED" : "ENABLED",
               text_color=trading_disabled ? color.red : color.green)
    table.cell(info_table, 0, 2, "H1 Trend", text_color=color.black)
    table.cell(info_table, 1, 2, wma8_h1 > wma16_h1 ? "UP" : "DOWN",
               text_color=wma8_h1 > wma16_h1 ? color.green : color.red)
    table.cell(info_table, 0, 3, "Position", text_color=color.black)
    table.cell(info_table, 1, 3, strategy.position_size > 0 ? "LONG" : strategy.position_size < 0 ? "SHORT" : "NONE",
               text_color=strategy.position_size != 0 ? color.blue : color.gray)
    table.cell(info_table, 0, 4, "Bars Since Win", text_color=color.black)
    table.cell(info_table, 1, 4, str.tostring(bars_since_win), text_color=color.black)
    table.cell(info_table, 0, 5, "Can Trade", text_color=color.black)
    table.cell(info_table, 1, 5, can_trade and can_trade_after_win ? "YES" : "NO",
               text_color=can_trade and can_trade_after_win ? color.green : color.red)
