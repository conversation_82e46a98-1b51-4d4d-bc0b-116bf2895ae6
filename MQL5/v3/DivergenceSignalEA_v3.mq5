//+------------------------------------------------------------------+
//|                                    DivergenceSignalEA_v3.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "3.00"

#include <Trade\Trade.mqh>

//--- Input parameters (keeping all original inputs)
input int      min_target = 7;           // Minimum target in pips
input int      max_target = 20;          // Maximum target in pips
input int      TSL1 = 5;                 // First trailing stop level in pips
input int      TSL2 = 10;                // Second trailing stop level in pips
input bool     limite_hours = false;     // Limit trading hours
input string   trade_hours_start = "10:00"; // Trading start time
input string   trade_hours_stop = "22:00";  // Trading stop time
input double   lot_size = 0.1;           // Lot size for trades
input int      magic_number = 12345;     // Magic number for trades

//--- v3 New input parameters
input int      sl_adjustment_pips = 3;   // SL adjustment after recent bottoms/tops (pips)
input ENUM_TRADE_TYPE trade_type = TRADE_TYPE_BOTH; // Type of trades: Long, Short, or Both
input string   trend_tf = "1H";         // Trend timeframe
input string   signal_tf = "1M";        // Signal timeframe
input bool     no_trade_after_win = true; // No trade after winning trade within same hour
input int      candles_after_win = 10;  // Number of candles after win to open new trade
input bool     check_price_action = false; // Check price action confirmation
input bool     send_notification = false; // Send push notifications

//--- Trade type enumeration
enum ENUM_TRADE_TYPE
{
    TRADE_TYPE_LONG_ONLY,   // Long Only
    TRADE_TYPE_SHORT_ONLY,  // Short Only
    TRADE_TYPE_BOTH         // Both Long and Short
};

//--- Global variables
CTrade trade;
int wma4_handle_trend, wma8_handle_trend, wma16_handle_trend;
int wma50_handle_signal;
int rsi_handle_signal, macd_handle_signal, stoch_handle_signal;

// Price action confirmation handles
int pa_handle_tf1, pa_handle_tf2; // For higher timeframes

double wma4_trend[], wma8_trend[], wma16_trend[];
double wma50_signal[];
double rsi_signal[], macd_main[], macd_signal[], stoch_main[], stoch_signal[];

double entry_price = 0;
datetime last_signal_time = 0;
datetime last_win_time = 0;
bool position_moved_to_breakeven = false;
bool position_moved_to_tsl2 = false;

// v3 variables
ENUM_TIMEFRAMES trend_timeframe;
ENUM_TIMEFRAMES signal_timeframe;
ENUM_TIMEFRAMES pa_timeframe1, pa_timeframe2; // Price action confirmation timeframes
bool last_trade_was_win = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set trade parameters
    trade.SetExpertMagicNumber(magic_number);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    //--- Convert string timeframes to enum
    trend_timeframe = StringToTimeframe(trend_tf);
    signal_timeframe = StringToTimeframe(signal_tf);
    
    if(trend_timeframe == PERIOD_CURRENT || signal_timeframe == PERIOD_CURRENT)
    {
        Print("Invalid timeframe specified");
        return(INIT_FAILED);
    }
    
    //--- Set price action confirmation timeframes
    SetPriceActionTimeframes();
    
    //--- Initialize indicator handles for trend timeframe
    wma4_handle_trend = iMA(_Symbol, trend_timeframe, 4, 0, MODE_LWMA, PRICE_CLOSE);
    wma8_handle_trend = iMA(_Symbol, trend_timeframe, 8, 0, MODE_LWMA, PRICE_CLOSE);
    wma16_handle_trend = iMA(_Symbol, trend_timeframe, 16, 0, MODE_LWMA, PRICE_CLOSE);
    
    //--- Initialize indicator handles for signal timeframe
    wma50_handle_signal = iMA(_Symbol, signal_timeframe, 50, 0, MODE_LWMA, PRICE_CLOSE);
    rsi_handle_signal = iRSI(_Symbol, signal_timeframe, 7, PRICE_CLOSE);
    macd_handle_signal = iMACD(_Symbol, signal_timeframe, 12, 26, 9, PRICE_CLOSE);
    stoch_handle_signal = iStochastic(_Symbol, signal_timeframe, 5, 3, 3, MODE_SMA, STO_LOWHIGH);
    
    //--- Check if handles are valid
    if(wma4_handle_trend == INVALID_HANDLE || wma8_handle_trend == INVALID_HANDLE || 
       wma16_handle_trend == INVALID_HANDLE || wma50_handle_signal == INVALID_HANDLE ||
       rsi_handle_signal == INVALID_HANDLE || macd_handle_signal == INVALID_HANDLE ||
       stoch_handle_signal == INVALID_HANDLE)
    {
        Print("Error creating indicator handles");
        return(INIT_FAILED);
    }
    
    //--- Resize arrays
    ArraySetAsSeries(wma4_trend, true);
    ArraySetAsSeries(wma8_trend, true);
    ArraySetAsSeries(wma16_trend, true);
    ArraySetAsSeries(wma50_signal, true);
    ArraySetAsSeries(rsi_signal, true);
    ArraySetAsSeries(macd_main, true);
    ArraySetAsSeries(macd_signal, true);
    ArraySetAsSeries(stoch_main, true);
    ArraySetAsSeries(stoch_signal, true);
    
    Print("DivergenceSignalEA v3.0 initialized successfully");
    Print("Trend TF: ", trend_tf, " | Signal TF: ", signal_tf);
    Print("Trade Type: ", EnumToString(trade_type));
    Print("Price Action Check: ", check_price_action ? "Enabled" : "Disabled");
    Print("Push Notifications: ", send_notification ? "Enabled" : "Disabled");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handles
    if(wma4_handle_trend != INVALID_HANDLE) IndicatorRelease(wma4_handle_trend);
    if(wma8_handle_trend != INVALID_HANDLE) IndicatorRelease(wma8_handle_trend);
    if(wma16_handle_trend != INVALID_HANDLE) IndicatorRelease(wma16_handle_trend);
    if(wma50_handle_signal != INVALID_HANDLE) IndicatorRelease(wma50_handle_signal);
    if(rsi_handle_signal != INVALID_HANDLE) IndicatorRelease(rsi_handle_signal);
    if(macd_handle_signal != INVALID_HANDLE) IndicatorRelease(macd_handle_signal);
    if(stoch_handle_signal != INVALID_HANDLE) IndicatorRelease(stoch_handle_signal);
    
    Print("DivergenceSignalEA v3.0 deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check for new signals
    CheckForSignals();
    
    //--- Manage existing positions
    ManagePositions();
}

//+------------------------------------------------------------------+
//| Convert string timeframe to enum                                 |
//+------------------------------------------------------------------+
ENUM_TIMEFRAMES StringToTimeframe(string tf_string)
{
    if(tf_string == "1M" || tf_string == "M1") return PERIOD_M1;
    if(tf_string == "5M" || tf_string == "M5") return PERIOD_M5;
    if(tf_string == "15M" || tf_string == "M15") return PERIOD_M15;
    if(tf_string == "30M" || tf_string == "M30") return PERIOD_M30;
    if(tf_string == "1H" || tf_string == "H1") return PERIOD_H1;
    if(tf_string == "4H" || tf_string == "H4") return PERIOD_H4;
    if(tf_string == "1D" || tf_string == "D1") return PERIOD_D1;
    if(tf_string == "1W" || tf_string == "W1") return PERIOD_W1;
    if(tf_string == "1MN" || tf_string == "MN1") return PERIOD_MN1;
    
    return PERIOD_CURRENT; // Invalid
}

//+------------------------------------------------------------------+
//| Set price action confirmation timeframes                        |
//+------------------------------------------------------------------+
void SetPriceActionTimeframes()
{
    // Set the first 2 higher timeframes from signal timeframe
    if(signal_timeframe == PERIOD_M1)
    {
        pa_timeframe1 = PERIOD_M5;
        pa_timeframe2 = PERIOD_M15;
    }
    else if(signal_timeframe == PERIOD_M5)
    {
        pa_timeframe1 = PERIOD_M15;
        pa_timeframe2 = PERIOD_M30;
    }
    else if(signal_timeframe == PERIOD_M15)
    {
        pa_timeframe1 = PERIOD_M30;
        pa_timeframe2 = PERIOD_H1;
    }
    else if(signal_timeframe == PERIOD_M30)
    {
        pa_timeframe1 = PERIOD_H1;
        pa_timeframe2 = PERIOD_H4;
    }
    else if(signal_timeframe == PERIOD_H1)
    {
        pa_timeframe1 = PERIOD_H4;
        pa_timeframe2 = PERIOD_D1;
    }
    else
    {
        // Default fallback
        pa_timeframe1 = PERIOD_M5;
        pa_timeframe2 = PERIOD_M15;
    }
    
    Print("Price Action TF1: ", EnumToString(pa_timeframe1), " | TF2: ", EnumToString(pa_timeframe2));
}
