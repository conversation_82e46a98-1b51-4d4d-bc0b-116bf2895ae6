Task1:

create v3 version of v1 @/v1/DivergenceSignalEA.mq5 expert with following changes:

create v3 inside folder /v3

keep existing condition and logic, keep all existing inputs and logic. Keep entry and exit conditions as is. keep stop loss at recent multiple bottoms/tops with divergence and trailing stoploss as is. only make the following changes:

1- add input to set value of sl after recent multiple bottoms/tops with divergence default value 3 pips, don't change current sl calculation just make it input with default value 3

2- add input to select type of trades to run, long, short, or both

4- do not open trade after winning trade within the same hour, make it input with default value true [input_var : no_trade_after_win=True] and input to set number of candles after win to open new trade [input_var : candles_after_win=10]

make ajustment to set the time frames as input. Trend TF is the higher time frame used to define the trading direction buy or sell, and signal TF is the smaller time frame used to analyze price behaviour and signal trade entry. 
input_var : trend_tf="1H"
input_var : signal_tf="1M"

5- add new confirmation to entry signals by adding option to check price action in addition to indicators divergence, make it input with default value false [input_var : check_price_action=false] it should check in the first 2 higher time frame from the signal time frame, for example if signal_tf="1M" it will check in "5M" and "15M" time frame, if signal_tf="5M" it will check in "15M" and "30M" time frame, if check_price_action is true and there is a confirmation in price action it will open a trade. if there is no divergence in indicators but there is a confirmation in price action it will open a trade. 
price action candles confirmation: 
pinbar, rejction for a level
inside bar breakout
engulfing candle

This condition will work only if check_price_action=true

6- add notification to use push notification service to send notifications to mobile device when there is a new entry signal, trad open, or trade close. with details of the trade and indicators values and profit/loss if any.
 make it input with default value false [input_var : send_notification=false]